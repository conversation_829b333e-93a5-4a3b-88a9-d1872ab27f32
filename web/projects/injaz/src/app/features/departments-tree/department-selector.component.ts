import {
    Component,
    Input,
    Output,
    EventEmitter,
    OnInit,
    OnDestroy,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Subject } from 'rxjs';
import {
    debounceTime,
    distinctUntilChanged,
    switchMap,
    takeUntil,
    startWith,
} from 'rxjs/operators';
import { DepartmentService } from './department.service';

export interface DepartmentTreeDto {
    id: string;
    name: string;
    fullPath: string;
    levelNumber: number;
    order: number;
    isMain: boolean;
    isPoliceStation: boolean;
    parentDepartmentId?: string;
    children: DepartmentTreeDto[];
}

export interface DepartmentSearchDto {
    id: string;
    name: string;
    fullPath: string;
    parentNames: string;
    levelNumber: number;
    isMain: boolean;
    isPoliceStation: boolean;
}

@Component({
    selector: 'app-department-selector',
    template: `
        <div
            class="department-selector"
            [attr.dir]="currentLanguage === 'ar' ? 'rtl' : 'ltr'"
        >
            <!-- Selection Mode Toggle -->
            <div class="selection-mode-toggle mb-3">
                <button
                    type="button"
                    class="btn btn-sm"
                    [class.btn-primary]="selectionMode === 'search'"
                    [class.btn-outline-primary]="selectionMode !== 'search'"
                    (click)="setSelectionMode('search')"
                >
                    {{ currentLanguage === 'ar' ? 'بحث' : 'Search' }}
                </button>
                <button
                    type="button"
                    class="btn btn-sm ms-2"
                    [class.btn-primary]="selectionMode === 'tree'"
                    [class.btn-outline-primary]="selectionMode !== 'tree'"
                    (click)="setSelectionMode('tree')"
                >
                    {{ currentLanguage === 'ar' ? 'شجرة' : 'Tree View' }}
                </button>
            </div>

            <!-- Search Mode -->
            <div *ngIf="selectionMode === 'search'" class="search-mode">
                <div class="form-group">
                    <label class="form-label">
                        {{
                            currentLanguage === 'ar'
                                ? 'البحث عن القسم'
                                : 'Search Department'
                        }}
                    </label>
                    <input
                        type="text"
                        class="form-control"
                        [formControl]="searchControl"
                        [placeholder]="
                            currentLanguage === 'ar'
                                ? 'اكتب لبدء البحث...'
                                : 'Type to search...'
                        "
                        autocomplete="off"
                    />
                </div>

                <!-- Search Results -->
                <div
                    class="search-results mt-2"
                    *ngIf="searchResults.length > 0"
                >
                    <div class="list-group max-height-300 overflow-auto">
                        <button
                            type="button"
                            class="list-group-item list-group-item-action"
                            *ngFor="
                                let dept of searchResults;
                                trackBy: trackByDepartmentId
                            "
                            [class.active]="selectedDepartment?.id === dept.id"
                            (click)="selectDepartment(dept)"
                        >
                            <div
                                class="d-flex justify-content-between align-items-start"
                            >
                                <div class="department-info flex-grow-1">
                                    <div class="department-name fw-bold">
                                        {{ dept.name }}
                                    </div>
                                    <div
                                        class="department-path text-muted small"
                                        *ngIf="dept.parentNames"
                                    >
                                        <i class="fas fa-sitemap me-1"></i>
                                        {{ dept.parentNames }}
                                    </div>
                                    <div
                                        class="department-level text-muted small"
                                    >
                                        {{
                                            currentLanguage === 'ar'
                                                ? 'المستوى'
                                                : 'Level'
                                        }}
                                        {{ dept.levelNumber }}
                                        <span
                                            *ngIf="dept.isMain"
                                            class="badge ms-1 bg-primary"
                                        >
                                            {{
                                                currentLanguage === 'ar'
                                                    ? 'رئيسي'
                                                    : 'Main'
                                            }}
                                        </span>
                                        <span
                                            *ngIf="dept.isPoliceStation"
                                            class="bg-success badge ms-1"
                                        >
                                            {{
                                                currentLanguage === 'ar'
                                                    ? 'مركز شرطة'
                                                    : 'Police Station'
                                            }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- No Results -->
                <div
                    *ngIf="searchExecuted && searchResults.length === 0"
                    class="alert alert-info"
                >
                    {{
                        currentLanguage === 'ar'
                            ? 'لا توجد نتائج'
                            : 'No results found'
                    }}
                </div>

                <!-- Loading -->
                <div *ngIf="isLoading" class="mt-2 text-center">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>

            <!-- Tree Mode -->
            <div *ngIf="selectionMode === 'tree'" class="tree-mode">
                <div
                    class="tree-container max-height-400 overflow-auto rounded border p-3"
                >
                    <div
                        *ngIf="departmentTree.length === 0 && !isLoading"
                        class="text-muted text-center"
                    >
                        {{
                            currentLanguage === 'ar'
                                ? 'لا توجد أقسام'
                                : 'No departments available'
                        }}
                    </div>

                    <div *ngIf="isLoading" class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>

                    <div
                        *ngFor="
                            let dept of departmentTree;
                            trackBy: trackByDepartmentId
                        "
                    >
                        <app-department-tree-node
                            [department]="dept"
                            [selectedDepartment]="selectedDepartment"
                            [currentLanguage]="currentLanguage"
                            (departmentSelected)="selectDepartment($event)"
                        >
                        </app-department-tree-node>
                    </div>
                </div>
            </div>

            <!-- Selected Department Display -->
            <div
                *ngIf="selectedDepartment"
                class="selected-department bg-light mt-3 rounded border p-3"
            >
                <h6 class="mb-2">
                    {{
                        currentLanguage === 'ar'
                            ? 'القسم المختار'
                            : 'Selected Department'
                    }}
                </h6>
                <div class="department-name fw-bold">
                    {{ selectedDepartment.name }}
                </div>
                <div
                    class="department-path text-muted small"
                    *ngIf="selectedDepartment.fullPath"
                >
                    <i class="fas fa-sitemap me-1"></i>
                    {{ selectedDepartment.fullPath }}
                </div>
                <button
                    type="button"
                    class="btn btn-sm btn-outline-danger mt-2"
                    (click)="clearSelection()"
                >
                    {{
                        currentLanguage === 'ar'
                            ? 'إلغاء الاختيار'
                            : 'Clear Selection'
                    }}
                </button>
            </div>
        </div>
    `,
    styles: [
        `
            .department-selector {
                width: 100%;
            }

            .max-height-300 {
                max-height: 300px;
            }

            .max-height-400 {
                max-height: 400px;
            }

            .department-name {
                color: #333;
            }

            .department-path {
                margin-top: 0.25rem;
            }

            .department-level {
                margin-top: 0.25rem;
            }

            .list-group-item {
                border-left: 3px solid transparent;
                transition: all 0.2s;
            }

            .list-group-item:hover {
                border-left-color: #007bff;
            }

            .list-group-item.active {
                border-left-color: #007bff;
                background-color: #f8f9fa;
                color: #333;
            }

            .selection-mode-toggle {
                display: flex;
                gap: 0.5rem;
            }

            .tree-container {
                background-color: #fafafa;
            }

            .selected-department {
                background-color: #e8f4f8 !important;
                border-color: #007bff !important;
            }

            /* RTL Support */
            [dir='rtl'] .list-group-item {
                border-left: none;
                border-right: 3px solid transparent;
            }

            [dir='rtl'] .list-group-item:hover {
                border-right-color: #007bff;
            }

            [dir='rtl'] .list-group-item.active {
                border-right-color: #007bff;
            }
        `,
    ],
})
export class DepartmentSelectorComponent implements OnInit, OnDestroy {
    @Input() public currentLanguage: string = 'en';
    @Input() public organizationTypeId?: string;
    @Input() public selectedDepartment?:
        | DepartmentSearchDto
        | DepartmentTreeDto;
    @Output() public departmentSelected = new EventEmitter<
        DepartmentSearchDto | DepartmentTreeDto
    >();

    public searchControl = new FormControl('');
    public selectionMode: 'search' | 'tree' = 'search';
    public departmentTree: DepartmentTreeDto[] = [];
    public searchResults: DepartmentSearchDto[] = [];
    public isLoading = false;
    public searchExecuted = false;

    private destroy$ = new Subject<void>();

    public constructor(private departmentService: DepartmentService) {}

    public ngOnInit(): void {
        this.setupSearchSubscription();
        if (this.selectionMode === 'tree') {
            this.loadDepartmentTree();
        }
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    public setSelectionMode(mode: 'search' | 'tree'): void {
        this.selectionMode = mode;
        if (mode === 'tree' && this.departmentTree.length === 0) {
            this.loadDepartmentTree();
        }
    }

    public selectDepartment(
        department: DepartmentSearchDto | DepartmentTreeDto
    ): void {
        this.selectedDepartment = department;
        this.departmentSelected.emit(department);
    }

    public clearSelection(): void {
        this.selectedDepartment = undefined;
        this.departmentSelected.emit(undefined);
    }

    public trackByDepartmentId(
        _index: number,
        item: DepartmentSearchDto | DepartmentTreeDto
    ): string {
        return item.id;
    }

    private setupSearchSubscription(): void {
        this.searchControl.valueChanges
            .pipe(
                startWith(''),
                debounceTime(300),
                distinctUntilChanged(),
                switchMap((searchTerm: string | null) => {
                    if (!searchTerm || searchTerm.length < 2) {
                        this.searchResults = [];
                        this.searchExecuted = false;
                        return [];
                    }

                    this.isLoading = true;
                    return this.departmentService.searchDepartments({
                        searchTerm,
                        language: this.currentLanguage,
                        organizationTypeId: this.organizationTypeId,
                        maxResults: 50,
                    });
                }),
                takeUntil(this.destroy$)
            )
            .subscribe(results => {
                this.searchResults = results;
                this.isLoading = false;
                this.searchExecuted = true;
            });
    }

    private loadDepartmentTree(): void {
        this.isLoading = true;
        this.departmentService
            .getDepartmentTree(this.currentLanguage, this.organizationTypeId)
            .pipe(takeUntil(this.destroy$))
            .subscribe(tree => {
                this.departmentTree = tree;
                this.isLoading = false;
            });
    }
}
