<!-- The form -->
<mnm-form
    content
    *ngIf="formState"
    [state]="formState"
    [translateLabels]="true"
>
    <!-- Department Selector -->
    <div class="form-group mb-3">
        <label class="form-label required">
            {{ 'translate_department' | translate }}
        </label>
        <app-department-selector
            [currentLanguage]="currentLanguage"
            [organizationTypeId]="organizationTypeId"
            [selectedDepartment]="selectedDepartment"
            (departmentSelected)="onDepartmentSelected($event)"
        ></app-department-selector>
        <div
            *ngIf="formState.isInvalid('department')"
            class="invalid-feedback d-block"
        >
            {{ 'translate_field_is_required' | translate }}
        </div>
    </div>

    <div class="mt-2 flex w-full flex-row justify-center">
        <button
            type="submit"
            class="btn btn-primary"
            (click)="submit()"
            [disabled]="isSubmitting"
        >
            <app-loading-ring
                *ngIf="isSubmitting"
                class="me-2"
            ></app-loading-ring>
            <i class="fa-light fa-paper-plane me-2"></i>
            <span>{{ 'translate_submit' | translate }}</span>
        </button>
    </div>
</mnm-form>

<div class="my-4 flex w-full flex-row items-center justify-center gap-2">
    <a class="text-gray-400" [routerLink]="'/auth/login'">
        {{ 'translate_go_to_login' | translate }}
    </a>
</div>
