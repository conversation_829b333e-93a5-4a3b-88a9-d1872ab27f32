import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import {
    DepartmentTreeDto,
    DepartmentSearchDto,
} from './department-selector.component';

export interface DepartmentSelectionRequest {
    searchTerm?: string;
    language?: string;
    maxResults?: number;
    includeFullTree?: boolean;
    organizationTypeId?: string;
}

@Injectable({
    providedIn: 'root',
})
export class DepartmentService {
    private readonly apiBaseUrl = '/api/departments';
    private treeCache = new Map<string, DepartmentTreeDto[]>();
    private cacheExpiry = new Map<string, number>();
    private readonly cacheDuration = 30 * 60 * 1000; // 30 minutes

    public constructor(private http: HttpClient) {}

    public getDepartmentTree(
        language: string = 'en',
        organizationTypeId?: string
    ): Observable<DepartmentTreeDto[]> {
        const cache_key = `tree_${language}_${organizationTypeId || 'all'}`;

        // Check cache first
        if (this.isValidCache(cache_key)) {
            return of(this.treeCache.get(cache_key)!);
        }

        let params = new HttpParams().set('lang', language);
        if (organizationTypeId) {
            params = params.set('organizationTypeId', organizationTypeId);
        }

        return this.http
            .get<DepartmentTreeDto[]>(`${this.apiBaseUrl}/tree`, { params })
            .pipe(
                map(response => {
                    // Cache the response
                    this.treeCache.set(cache_key, response);
                    this.cacheExpiry.set(
                        cache_key,
                        Date.now() + this.cacheDuration
                    );
                    return response;
                }),
                catchError(error => {
                    console.error('Error loading department tree:', error);
                    return of([]);
                })
            );
    }

    public searchDepartments(
        request: DepartmentSelectionRequest
    ): Observable<DepartmentSearchDto[]> {
        const body = {
            searchTerm: request.searchTerm || '',
            language: request.language || 'en',
            maxResults: request.maxResults || 50,
            includeFullTree: request.includeFullTree || false,
            organizationTypeId: request.organizationTypeId,
        };

        return this.http
            .post<DepartmentSearchDto[]>(`${this.apiBaseUrl}/search`, body)
            .pipe(
                catchError(error => {
                    console.error('Error searching departments:', error);
                    return of([]);
                })
            );
    }

    public getDepartmentById(
        id: string,
        language: string = 'en'
    ): Observable<DepartmentSearchDto | null> {
        return this.searchDepartments({
            searchTerm: '',
            language,
            maxResults: 1000,
        }).pipe(map(departments => departments.find(d => d.id === id) || null));
    }

    // Utility method to flatten tree for search operations
    public flattenDepartmentTree(
        tree: DepartmentTreeDto[]
    ): DepartmentTreeDto[] {
        const flattened: DepartmentTreeDto[] = [];

        const traverse = (nodes: DepartmentTreeDto[]): void => {
            for (const node of nodes) {
                flattened.push(node);
                if (node.children && node.children.length > 0) {
                    traverse(node.children);
                }
            }
        };

        traverse(tree);
        return flattened;
    }

    // Clear cache (useful for data refresh)
    public clearCache(): void {
        this.treeCache.clear();
        this.cacheExpiry.clear();
    }

    private isValidCache(key: string): boolean {
        if (!this.treeCache.has(key) || !this.cacheExpiry.has(key)) {
            return false;
        }

        return Date.now() < this.cacheExpiry.get(key)!;
    }
}
